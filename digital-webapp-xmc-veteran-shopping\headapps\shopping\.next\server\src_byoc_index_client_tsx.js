"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "src_byoc_index_client_tsx";
exports.ids = ["src_byoc_index_client_tsx"];
exports.modules = {

/***/ "./src/byoc/index.client.tsx":
/*!***********************************!*\
  !*** ./src/byoc/index.client.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @sitecore-feaas/clientside/react */ \"@sitecore-feaas/clientside/react\");\n/* harmony import */ var _sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @sitecore/components/form */ \"@sitecore/components/form\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__]);\n_sitecore_components_form__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n/**\r\n * Below are Sitecore default BYOC components. Included components will be available in Pages and Components apps out of the\r\n * box for convenience. It is advised to comment out unused components when application is ready for production\r\n * to reduce javascript bundle size.\r\n */ // SitecoreForm component displays forms created in XM Forms as individual components to be embedded into Pages.\n// Sitecore Forms for Sitecore XP are still available separately via @sitecore-jss-forms package\n\n/**\r\n * End of built-in JSS imports\r\n * You can import your own client component below\r\n * @example\r\n * import './MyClientComponent';\r\n * @example\r\n * import 'src/otherFolder/MyOtherComponent';\r\n */ // An important boilerplate component that prevents BYOC components from being optimized away and allows then. Should be kept in this file.\nconst ClientsideComponent = (props)=>_sitecore_feaas_clientside_react__WEBPACK_IMPORTED_MODULE_0__.ExternalComponent(props);\n/**\r\n * Clientside BYOC component will be rendered in the browser, so that external components:\r\n * - Can have access to DOM apis, including network requests\r\n * - Use clientside react hooks like useEffect.\r\n * - Be implemented as web components.\r\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientsideComponent);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/byoc/index.client.tsx\n");

/***/ })

};
;