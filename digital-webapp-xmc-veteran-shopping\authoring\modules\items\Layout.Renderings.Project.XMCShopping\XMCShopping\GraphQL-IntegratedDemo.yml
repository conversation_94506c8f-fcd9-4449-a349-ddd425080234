﻿---
ID: "a8114f28-5e5e-5d60-a571-0ad56541a20c"
Parent: "9f444b3b-4718-498c-97f7-7ed0afbeae87"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: "/sitecore/layout/Renderings/Project/XMCShopping/GraphQL-IntegratedDemo"
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: "GraphQL-IntegratedDemo"
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/16x16/graph_connection_directed.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    # This file contains a GraphQL query that will be executed and the result provided to
    # your JSS component. You can run this query in GraphiQL ($endpoint/ui) for a nice editing experience.
    
    # Note that we're executing _two queries_ (datasource and contextItem)
    # within the context of the IntegratedDemoQuery _operation_. This makes it
    # very efficient at gathering data from multiple sources.
    
    query IntegratedDemoQuery($datasource: String!, $contextItem: String!, $language: String!) {
      # Datasource query
      # $datasource will always be set to the ID of the rendering's datasource item
      # (as long as the GraphQLData helper is used)
      datasource: item(path: $datasource, language: $language) {
        id
        name
        # Strongly-typed querying on known templates is possible!
        ...on GraphQLIntegratedDemo {
          # Single-line text field
          sample1 {
            # the 'jsonValue' field is a JSON blob that represents the object that
            # should be passed to JSS field rendering helpers (i.e. text, image, link)
            jsonValue
            value
          }
          # General Link field
          sample2 {
            jsonValue
            # Structured querying of the field's values is possible
            text
            target
            url
            # Access to the template definition is possible
            definition {
              type
              shared
            }
          }
        }
      }
    
      # Context/route item query
      # $contextItem will always be set to the ID of the current context item (the route item)
      # (as long as the GraphQLData helper is used)
      contextItem: item(path: $contextItem, language: $language) {
        id
        # Get the page title from the app route template
        ...on AppRoute {
          pageTitle {
            value
          }
        }
    
        # List the children of the current route
        children(hasLayout: true) {
          results {
            id
            # typing fragments can be used anywhere!
            # so in this case, we're grabbing the 'pageTitle'
            # field on all child route items.
            ...on AppRoute {
              pageTitle {
                jsonValue
                value
              }
            }
            url{
              path
            }
          }
        }
      }
    }
    
- ID: "1a7c85e5-dc0b-490d-9187-bb1dbcb4c72f"
  Hint: Datasource Template
  Value: "/sitecore/templates/Project/Shopping/GraphQL-IntegratedDemo"
- ID: "9c6106ea-7a5a-48e2-8cad-f0f693b1e2d4"
  Hint: __Read Only
  Value: 
- ID: "b5b27af1-25ef-405c-87ce-369b3a004016"
  Hint: Datasource Location
  Value: "./Page Components|/sitecore/content/TXU/Shopping/Components/GraphQL-IntegratedDemo"
- ID: "ba3f86a2-4a1c-4d78-b63d-91c2779c1b5e"
  Hint: __Sortorder
  Value: 200
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "5e37be40-d20a-420d-86e6-452aaa4056fe"
