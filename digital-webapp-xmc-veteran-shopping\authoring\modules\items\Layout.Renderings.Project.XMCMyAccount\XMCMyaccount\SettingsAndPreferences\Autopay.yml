﻿---
ID: "01b2b7ba-5e68-4392-a141-58010db53d19"
Parent: "2af847b3-5589-4894-a300-1f9659f840fa"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/SettingsAndPreferences/Autopay
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: Autopay
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: "{********-F5CD-4B41-B8CE-B278D92F60D7}"
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/money_bill_fire.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AutopayQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        PaymentMethodsTitleText: field(name: "PaymentMethodsTitleText") {
          value
        }
        SaveChangesBtnText: field(name: "SaveChangesBtnText") {
          value
        }
        CancelChangesBtnText: field(name: "CancelChangesBtnText") {
          value
        }
        AutoPaySubText: field(name: "AutoPaySubText") {
          value
        }
        AutoPayStatusText: field(name: "AutoPayStatusText") {
          value
        }
        AutoPayTurnOffText: field(name: "AutoPayTurnOffText") {
          value
        }
        AutoPayTurnOnText: field(name: "AutoPayTurnOnText") {
          value
        }
        AutoPayTurnOffBtnText: field(name: "AutoPayTurnOffBtnText") {
          value
        }
        AutoPayTurnOnBtnText: field(name: "AutoPayTurnOnBtnText") {
          value
        }
        ManagePaymentMethodsSubHeaderText: field(name: "ManagePaymentMethodsSubHeaderText") {
          value
        }
        TermsAndConditionText: field(name: "TermsAndConditionText") {
          value
        }
        IAcceptText: field(name: "IAcceptText") {
          value
        }
        AutoPayNotEligibleMessage: field(name: "AutoPayNotEligibleMessage") {
          value
        }
        DefaultErrorMessage: field(name: "DefaultErrorMessage") {
          value
        }
        AcceptErrorMessage: field(name: "AcceptErrorMessage") {
          value
        }
        SelectPaymentMethodErrorMessage: field(name: "SelectPaymentMethodErrorMessage") {
          value
        }
        PaymentMethodText: field(name: "PaymentMethodText") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        CardEndingText: field(name: "CardEndingText") {
          value
        }
        EditText: field(name: "EditText") {
          value
        }
        AutoPayTermsAndConditionLink: field(name: "AutoPayTermsAndConditionLink") {
          jsonValue
        }
        AutoPayTurnOnPastDueWarningMessage: field(name: "AutoPayTurnOnPastDueWarningMessage") {
          value
        }
        AutoPayTurnOffPastDueWarningMessage: field(name: "AutoPayTurnOffPastDueWarningMessage") {
          value
        }
      }
    }
    
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "b735afd5-93c9-4d14-95b8-d291cfc625b7"
