{
  "files.associations": {
    "*.sicpackage": "json"
  },
  "json.schemas": [
    {
      "fileMatch": ["/sitecore.json"],
      "url": "./.sitecore/schemas/RootConfigurationFile.schema.json"
    },
    {
      "fileMatch": ["/.sitecore/user.json"],
      "url": "./.sitecore/schemas/UserConfiguration.schema.json"
    },
    {
      "fileMatch": ["*.module.json"],
      "url": "./.sitecore/schemas/ModuleFile.schema.json"
    }
  ],
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,

  // i read json files being auto formatted can cause issues
  // so just to be safe lets not auto format json
  // you can remove this at any time if validated its safe
  "[json]": {
    "editor.formatOnSave": false
  }
}
