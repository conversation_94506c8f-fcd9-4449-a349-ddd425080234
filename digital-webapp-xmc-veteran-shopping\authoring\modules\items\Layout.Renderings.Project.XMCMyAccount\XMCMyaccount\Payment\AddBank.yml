﻿---
ID: "f1c1736f-0a0b-48e9-b560-9eb2d31492ea"
Parent: "e13e91f1-a791-4889-ba15-5a8aa1f9aec3"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/AddBank
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: AddBank
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/moneybag_coins.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query AddBankFieldsQuery($datasource: String!, $language: String!) {
       item(path: $datasource, language: $language) {
        AddBankAccountTitle: field(name: "AddBankAccountTitle") {
          value
        }
        AccountDetailsText: field(name: "AccountDetailsText") {
          value
        }
        BankAccountTypeText: field(name: "BankAccountTypeText") {
          value
        }
        NickNamePaymentMethodText: field(name: "NickNamePaymentMethodText") {
          value
        }
        SelectAccountTypeText: field(name: "SelectAccountTypeText") {
          value
        }
        AccountHolderNameText: field(name: "AccountHolderNameText") {
          value
        }
        RoutingNumberText: field(name: "RoutingNumberText") {
          value
        }
        BankAccNumberText: field(name: "BankAccNumberText") {
          value
        }
        ConfirmAccNumberText: field(name: "ConfirmAccNumberText") {
          value
        }
        BankPaymentSaveText: field(name: "BankPaymentSaveText") {
          value
        }
        BankPaymentDefaultSaveText: field(name: "BankPaymentDefaultSaveText") {
          value
        }
        AddButton: field(name: "AddButton") {
          value
        }
        BackButton: field(name: "BackButton") {
          value
        }
        AllowOneTimePayment: field(name: "AllowOneTimePayment") {
          value
        }
        AddEditBankText: field(name: "AddEditBankText") {
          value
        }
        AddEditBankAccountTitle: field(name: "AddEditBankAccountTitle") {
          value
        }
        PaymentAmountText: field(name: "PaymentAmountText") {
          value
        }
        PaymentDateText: field(name: "PaymentDateText") {
          value
        }
        ContinuePaymentButtonText: field(name: "ContinuePaymentButtonText") {
          value
        }
        DefaultErrorMessage: field(name: "DefaultErrorMessage") {
          value
        }
        AccountTypeErrorText: field(name: "AccountTypeErrorText") {
          value
        }
        BankNameErrorText: field(name: "BankNameErrorText") {
          value
        }
        AccountHolderNameErrorText: field(name: "AccountHolderNameErrorText") {
          value
        }
        AccountHolderAlphaNumericErrorText: field(name: "AccountHolderAlphaNumericErrorText") {
          value
        }
        RountingNumberErrorText: field(name: "RountingNumberErrorText") {
          value
        }
        BankAccountNumberErrorText: field(name: "BankAccountNumberErrorText") {
          value
        }
        ConfirmBankAccountNumberErrorText: field(name: "ConfirmBankAccountNumberErrorText") {
          value
        }
        AccountNumbersMismatchErrorText: field(name: "AccountNumbersMismatchErrorText") {
          value
        }
        ReviewPaymentLink: field(name: "ReviewPaymentLink") {
          jsonValue
        }
        AccountAlreadyExistsErrorText: field(name: "AccountAlreadyExistsErrorText") {
          value
        }
        AccountEndingText: field(name: "AccountEndingText") {
          value
        }
        PaymentAddedTitle: field(name: "PaymentAddedTitle") {
          value
        }
        PaymentAddedMessage: field(name: "PaymentAddedMessage") {
          value
        }
        PaymentAddedButtonText: field(name: "PaymentAddedButtonText") {
          value
        }
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "a77e8568-1ab3-44f1-a664-b7c37ec7810d"
  Hint: Parameters Template
  Value: 
- ID: "ba3f86a2-4a1c-4d78-b63d-91c2779c1b5e"
  Hint: __Sortorder
  Value: 200
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "d3b2863a-ab01-47da-815c-449deac369f6"
