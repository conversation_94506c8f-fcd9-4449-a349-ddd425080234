﻿---
ID: "068f01e6-07bf-47a7-9cb0-571c8aae7d17"
Parent: "b94cfa7d-06ac-4846-ba10-f31436bd227c"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCShopping/Custom TXU/Common/ForgotPassword
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: ForgotPassword
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query ForgotPasswordQuery($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        ForgotPasswordTitle: field(name: "ForgotPasswordTitle") {
          value
        }
        ForgotPasswordSubTitle: field(name: "ForgotPasswordSubTitle") {
          value
        }
        EmailAddressLabel: field(name: "EmailAddressLabel") {
          value
        }
        NextButtonLabel: field(name: "NextButtonLabel") {
          value
        }
        CancelButtonLabel: field(name: "CancelButtonLabel") {
          value
        }
        SecurityValidationTitle: field(name: "SecurityValidationTitle") {
          value
        }
        SecurityValidationSubTitle: field(name: "SecurityValidationSubTitle") {
          value
        }
        YourSecurityQuestionLabel: field(name: "YourSecurityQuestionLabel") {
          value
        }
        SecurityAnswerSensitiveLabel: field(name: "SecurityAnswerSensitiveLabel") {
          value
        }
        LastFourDigitsSSNLabel: field(name: "LastFourDigitsSSNLabel") {
          value
        }
        MothersMaidenNameLabel: field(name: "MothersMaidenNameLabel") {
          value
        }
        DriversLicenseLabel: field(name: "DriversLicenseLabel") {
          value
        }
        NewPasswordTitle: field(name: "NewPasswordTitle") {
          value
        }
        NewPasswordSubTitle: field(name: "NewPasswordSubTitle") {
          value
        }
        ConfirmPasswordPlaceholder: field(name: "ConfirmPasswordPlaceholder") {
          value
        }
        SavePasswordLabel: field(name: "SavePasswordLabel") {
          value
        }
        UsernameErrorMessage: field(name: "UsernameErrorMessage") {
          value
        }
        EnterAnswerErrorMessage: field(name: "EnterAnswerErrorMessage") {
          value
        }
        NewPasswordErrorMessage: field(name: "NewPasswordErrorMessage") {
          value
        }
        ConfirmPasswordErrorMessage: field(name: "ConfirmPasswordErrorMessage") {
          value
        }
        PasswordChangeSuccessText: field(name: "PasswordChangeSuccessText") {
          value
        }
        PasswordInfoText: field(name: "PasswordInfoText") {
          value
        }
        LoginPageUrl: field(name: "LoginPageUrl") {
          jsonValue
        }
        DefaultErrorMessage: field(name: "DefaultErrorMessage") {
          value
        }
        SecurityAnswerErrorMessage: field(name: "SecurityAnswerErrorMessage") {
          value
        }
        InvalidUserNameErrorMessage: field(name: "InvalidUserNameErrorMessage") {
          value
        }
        PasswordNotUpdatedErrorMessage: field(name: "PasswordNotUpdatedErrorMessage") {
          value
        }
        PasswordNotMatchErrorMessage: field(name: "PasswordNotMatchErrorMessage") {
          value
        }
        EmailAddressRequiredMessage: field(name: "EmailAddressRequiredMessage") {
          value
        }
        PasswordValidationErrorMessage: field(name: "PasswordValidationErrorMessage") {
          value
        }
        EmailValidationErrorMessage: field(name: "EmailValidationErrorMessage") {
          value
        }
        BackToLoginLabel: field(name: "BackToLoginLabel") {
          value
        }
        DateOfBirthLabel: field(name: "DateOfBirthLabel") {
          value
        }
        IsUserName: field(name: "IsUserName") {
          value
        }
        InvalidDateMessage: field(name: "InvalidDateMessage") {
          value
        }
        InvalidDateFormatMessage: field(name: "InvalidDateFormatMessage") {
          value
        }
        DriverLicenseStateLabel: field(name: "DriverLicenseStateLabel") {
          value
        }
        ContactCustomerCareText: field(name: "ContactCustomerCareText") {
          value
        }
        SSNMinLengthValidationErrorMessage: field(name: "SSNMinLengthValidationErrorMessage") {
          value
        }
        DriverLicenseStateValidationMessage: field(name: "DriverLicenseStateValidationMessage") {
          value
        }
        AccountLockedMessage: field(name: "AccountLockedMessage") {
          value
        }
    	 DriverLicenseState: field(name: "DriverLicenseState") {
          ... on MultilistField {
            targetItems {
              displayName
            }
          }
        }
      }
    }
    
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "19b88c7e-c872-490c-8058-b551a6fc3a58"
