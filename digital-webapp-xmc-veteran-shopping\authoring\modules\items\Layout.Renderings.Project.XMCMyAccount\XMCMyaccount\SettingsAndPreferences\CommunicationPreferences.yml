﻿---
ID: "2ff8687f-3ce7-424d-86b1-31b707dbcfe5"
Parent: "2af847b3-5589-4894-a300-1f9659f840fa"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/SettingsAndPreferences/CommunicationPreferences
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: CommunicationPreferences
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: 
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: office/32x32/list_style_numbered.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query CommunicationPreferencesFields($datasource: String!, $language: String!) {
      item(path: $datasource, language: $language) {
        BillAccountDeliveryText: field(name: "BillAccountDeliveryText") {
          value
        }
        BillAccountShortDescription: field(name: "BillAccountShortDescription") {
          value
        }
        BillAccountDescription: field(name: "BillAccountDescription") {
          value
        }
        BillAccountDescriptionText2: field(name: "BillAccountDescriptionText2") {
          value
        }
        EmailNoteText: field(name: "EmailNoteText") {
          value
        }
        AccountSettingsURL: field(name: "AccountSettingsURL") {
          value
        }
        ProfileSettingsURL: field(name: "ProfileSettingsURL") {
          value
        }
        MailText: field(name: "MailText") {
          value
        }
        EmailText: field(name: "EmailText") {
          value
        }
        SaveChangesButtonText: field(name: "SaveChangesButtonText") {
          value
        }
        CancelButtonText: field(name: "CancelButtonText") {
          value
        }
        SmsText: field(name: "SmsText") {
          value
        }
        TextNotificationsDescription: field(name: "TextNotificationsDescription") {
          value
        }
        SmsTermsAndConditions: field(name: "SmsTermsAndConditions") {
          value
        }
        SmsTermsAndConditionLink: field(name: "SmsTermsAndConditionLink") {
          value
        }
        SmsTermsAndCondirionsError: field(name: "SmsTermsAndCondirionsError") {
          value
        }
        GlobalOptSuccessMessage: field(name: "GlobalOptSuccessMessage") {
          value
        }
        CAOptSuccessMessage: field(name: "CAOptSuccessMessage") {
          value
        }
        SaveChangesFailureMessage: field(name: "SaveChangesFailureMessage") {
          value
        }
        TipsAlertTitle: field(name: "TipsAlertTitle") {
          value
        }
        TipsAlertDescription: field(name: "TipsAlertDescription") {
          value
        }
        AlertsText: field(name: "AlertsText") {
          value
        }
        PaymentDueAlertText: field(name: "PaymentDueAlertText") {
          value
        }
        BudgetAlertText: field(name: "BudgetAlertText") {
          value
        }
        UsageAlertText: field(name: "UsageAlertText") {
          value
        }
        WeeklySnapshotText: field(name: "WeeklySnapshotText") {
          value
        }
        CAOptEmailText: field(name: "CAOptEmailText") {
          value
        }
        CAOptSmsText: field(name: "CAOptSmsText") {
          value
        }
        CAOptSaveChangeButtonText: field(name: "CAOptSaveChangeButtonText") {
          value
        }
        CAOptCancelButtonText: field(name: "CAOptCancelButtonText") {
          value
        }
        DaysListForApp: field(name: "DaysListForApp") {
          value
        }
        UsageListForApp: field(name: "UsageListForApp") {
          value
        }
        UsageAlertInterval: field(name: "UsageAlertInterval") {
          value
        }
        BeforeDueDateText: field(name: "BeforeDueDateText") {
          value
        }
        UpdateMobileNumberText: field(name: "UpdateMobileNumberText") {
          value
        }
        PaymentDueErrorText: field(name: "PaymentDueErrorText") {
          value
        }
        UsageAlertErrorText: field(name: "UsageAlertErrorText") {
          value
        }
        BudgetAlertErrorText: field(name: "BudgetAlertErrorText") {
          value
        }
        SomethingWentWrongText: field(name: "SomethingWentWrongText") {
          value
        }
        SmsAlertErrorText: field(name: "SmsAlertErrorText") {
          value
        }
        TermsAndConditionsLink: field(name: "TermsAndConditionsLink") {
          jsonValue
        }
        TermsAndConditionsText: field(name: "TermsAndConditionsText") {
          value
        }
        HideTextToggle: field(name: "HideTextToggle") {
          value
        }
        CommunicationMethodRequredMsg: field(name: "CommunicationMethodRequredMsg") {
          value
        }
        DaysListForApp: field(name: "DaysListForApp") {
          ... on MultilistField {
            targetItems {
              Key: field(name: "Key") {
                value
              }
              Phrase: field(name: "Phrase") {
                value
              }
            }
          }
        }
        UsageListForApp: field(name: "UsageListForApp") {
          ... on MultilistField {
            targetItems {
              Key: field(name: "Key") {
                value
              }
              Phrase: field(name: "Phrase") {
                value
              }
            }
          }
        }
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "e06d0aea-1fbd-4d77-bf97-2f058f125c83"
