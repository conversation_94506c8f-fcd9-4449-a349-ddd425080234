﻿---
ID: "44cc73e5-fa1d-4ed1-bc0e-ef52adce1ba6"
Parent: "7bfdce5d-d1ea-4187-b2a0-87d498276811"
Template: "04646a89-996f-4ee7-878a-ffdbf1f0ef0d"
Path: /sitecore/layout/Renderings/Project/XMCMyaccount/Payment/Deferral/PickADate
SharedFields:
- ID: "037fe404-dd19-4bf7-8e30-4dadf68b27b0"
  Hint: componentName
  Value: PickADate
- ID: "069a8361-b1cd-437c-8c32-a3be78941446"
  Hint: Placeholders
  Value: |
    {995DA88F-9AE7-5F82-A0A3-A752442833F9}
    {26EF040E-E07B-4A23-83F5-4CF60FEE98F9}
- ID: "06d5295c-ed2f-4a54-9bf2-26228d113318"
  Hint: __Icon
  Value: Office/32x32/table_selection_all.png
- ID: "17bb046a-a32a-41b3-8315-81217947611b"
  Hint: ComponentQuery
  Value: |
    query PickADateQuery($datasource: String!, $language: String!) {
      datasource: item(path: $datasource, language: $language) {
        CurrentDisconnectDateText: field(name: "CurrentDisconnectDateText") {
          value
        }
        PaymentAmountDueText: field(name: "PaymentAmountDueText") {
          value
        }
        PayBillByText: field(name: "PayBillByText") {
          value
        }
        NoteText: field(name: "NoteText") {
          value
        }
        ConsentText: field(name: "ConsentText") {
          value
        }
        ContinueButtonText: field(name: "ContinueButtonText") {
          value
        }
        CancelButtonText: field(name: "CancelButtonText") {
          value
        }
      }
    }
    
- ID: "308e88f5-cd6e-4f8f-bafe-95a47dedefdc"
  Hint: Editable
  Value: 
- ID: "c39a90ce-0035-41bb-90f6-3c8a6ea87797"
  Hint: AddFieldEditorButton
  Value: 
- ID: "f172b787-7b88-4bd5-ae4d-6308e102ef11"
  Hint: Enable Datasource Query
  Value: 
Languages:
- Language: en
  Versions:
  - Version: 1
    Fields:
    - ID: "8cdc337e-a112-42fb-bbb4-4143751e123f"
      Hint: __Revision
      Value: "acea2d33-6afd-43b7-a0b8-1fb19c656352"
